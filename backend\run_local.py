"""
Local development server runner
"""

import os
import sys
import subprocess

def main():
    """Run the FastAPI server locally"""
    
    # Set environment variables for local development
    os.environ.setdefault("DATABASE_URL", "sqlite:///./sms_campaigns.db")
    os.environ.setdefault("REDIS_URL", "redis://localhost:6379/0")
    os.environ.setdefault("SECRET_KEY", "local-development-secret-key")
    os.environ.setdefault("DEBUG", "True")
    
    print("Starting SMS Campaign System (Local Development)")
    print("=" * 50)
    print("Database: SQLite (local file)")
    print("Redis: localhost:6379 (if available)")
    print("API: http://localhost:8000")
    print("Docs: http://localhost:8000/docs")
    print("=" * 50)
    
    try:
        # Run uvicorn server
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "app.main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ], check=True)
    except KeyboardInterrupt:
        print("\nShutting down server...")
    except subprocess.CalledProcessError as e:
        print(f"Error starting server: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
