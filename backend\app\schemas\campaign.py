"""
Campaign Pydantic schemas
"""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel
from app.models.campaign import CampaignStatus


class CampaignBase(BaseModel):
    name: str
    description: Optional[str] = None
    message_template: str
    segment_id: Optional[int] = None
    scheduled_at: Optional[datetime] = None


class CampaignCreate(CampaignBase):
    pass


class CampaignUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    message_template: Optional[str] = None
    segment_id: Optional[int] = None
    scheduled_at: Optional[datetime] = None
    status: Optional[CampaignStatus] = None


class Campaign(CampaignBase):
    id: int
    status: CampaignStatus
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    total_recipients: int = 0
    messages_sent: int = 0
    messages_delivered: int = 0
    messages_failed: int = 0
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
