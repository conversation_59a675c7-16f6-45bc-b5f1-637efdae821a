"""
User Pydantic schemas
"""

from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, validator


class UserBase(BaseModel):
    phone_number: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    metrics: Optional[Dict[str, Any]] = {}
    is_active: bool = True
    is_subscribed: bool = True

    @validator('phone_number')
    def validate_phone_number(cls, v):
        # Basic phone number validation
        if not v.startswith('+'):
            raise ValueError('Phone number must start with +')
        return v


class UserCreate(UserBase):
    pass


class UserUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    metrics: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    is_subscribed: Optional[bool] = None


class User(UserBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
