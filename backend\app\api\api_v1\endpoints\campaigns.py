"""
Campaign management endpoints
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

from app.core.database import get_db
from app.schemas.campaign import Campaign, CampaignCreate, CampaignUpdate
from app.services.campaign_service import CampaignService

router = APIRouter()


@router.get("/", response_model=List[Campaign])
async def get_campaigns(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get list of campaigns with pagination"""
    campaign_service = CampaignService(db)
    return await campaign_service.get_campaigns(skip=skip, limit=limit)


@router.post("/", response_model=Campaign)
async def create_campaign(
    campaign_data: CampaignCreate,
    db: Session = Depends(get_db)
):
    """Create new campaign"""
    campaign_service = CampaignService(db)
    return await campaign_service.create_campaign(campaign_data)


@router.get("/{campaign_id}", response_model=Campaign)
async def get_campaign(
    campaign_id: int,
    db: Session = Depends(get_db)
):
    """Get campaign by ID"""
    campaign_service = CampaignService(db)
    campaign = await campaign_service.get_campaign(campaign_id)
    if not campaign:
        raise HTTPException(status_code=404, detail="Campaign not found")
    return campaign


@router.put("/{campaign_id}", response_model=Campaign)
async def update_campaign(
    campaign_id: int,
    campaign_data: CampaignUpdate,
    db: Session = Depends(get_db)
):
    """Update campaign"""
    campaign_service = CampaignService(db)
    campaign = await campaign_service.update_campaign(campaign_id, campaign_data)
    if not campaign:
        raise HTTPException(status_code=404, detail="Campaign not found")
    return campaign


@router.post("/{campaign_id}/start")
async def start_campaign(
    campaign_id: int,
    db: Session = Depends(get_db)
):
    """Start campaign execution"""
    campaign_service = CampaignService(db)
    result = await campaign_service.start_campaign(campaign_id)
    if not result:
        raise HTTPException(status_code=404, detail="Campaign not found")
    return {"message": "Campaign started successfully", "task_id": result}


@router.post("/{campaign_id}/stop")
async def stop_campaign(
    campaign_id: int,
    db: Session = Depends(get_db)
):
    """Stop campaign execution"""
    campaign_service = CampaignService(db)
    success = await campaign_service.stop_campaign(campaign_id)
    if not success:
        raise HTTPException(status_code=404, detail="Campaign not found")
    return {"message": "Campaign stopped successfully"}
