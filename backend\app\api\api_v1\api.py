"""
API v1 router configuration
"""

from fastapi import APIRouter

from app.api.api_v1.endpoints import users, campaigns, segments, sms

api_router = APIRouter()

# Include endpoint routers
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(campaigns.router, prefix="/campaigns", tags=["campaigns"])
api_router.include_router(segments.router, prefix="/segments", tags=["segments"])
api_router.include_router(sms.router, prefix="/sms", tags=["sms"])
