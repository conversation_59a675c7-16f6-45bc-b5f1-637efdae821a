import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Container, AppBar, Toolbar, Typography, Box } from '@mui/material';
import Dashboard from './pages/Dashboard';
import Users from './pages/Users';
import Campaigns from './pages/Campaigns';
import Segments from './pages/Segments';
import Navigation from './components/Navigation';

function App() {
  return (
    <Box sx={{ flexGrow: 1 }}>
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            SMS Campaign System
          </Typography>
        </Toolbar>
      </AppBar>
      
      <Box sx={{ display: 'flex' }}>
        <Navigation />
        
        <Container component="main" sx={{ flexGrow: 1, p: 3 }}>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/users" element={<Users />} />
            <Route path="/campaigns" element={<Campaigns />} />
            <Route path="/segments" element={<Segments />} />
          </Routes>
        </Container>
      </Box>
    </Box>
  );
}

export default App;
