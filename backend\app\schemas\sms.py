"""
SMS Message Pydantic schemas
"""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel
from app.models.sms_message import MessageStatus


class SMSMessageBase(BaseModel):
    phone_number: str
    message_text: str


class SMSMessageCreate(SMSMessageBase):
    campaign_id: Optional[int] = None
    user_id: Optional[int] = None


class SMSMessage(SMSMessageBase):
    id: int
    status: MessageStatus
    external_id: Optional[str] = None
    error_message: Optional[str] = None
    campaign_id: Optional[int] = None
    user_id: Optional[int] = None
    created_at: datetime
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class SMSStatus(BaseModel):
    message_id: int
    status: MessageStatus
    external_id: Optional[str] = None
    error_message: Optional[str] = None
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
