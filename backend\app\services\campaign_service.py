"""
Campaign service for business logic
"""

from typing import List, Optional
from sqlalchemy.orm import Session
from app.models.campaign import Campaign, CampaignStatus
from app.schemas.campaign import CampaignCreate, CampaignUpdate


class CampaignService:
    def __init__(self, db: Session):
        self.db = db

    async def get_campaigns(self, skip: int = 0, limit: int = 100) -> List[Campaign]:
        """Get list of campaigns with pagination"""
        return self.db.query(Campaign).offset(skip).limit(limit).all()

    async def get_campaign(self, campaign_id: int) -> Optional[Campaign]:
        """Get campaign by ID"""
        return self.db.query(Campaign).filter(Campaign.id == campaign_id).first()

    async def create_campaign(self, campaign_data: CampaignCreate) -> Campaign:
        """Create new campaign"""
        db_campaign = Campaign(**campaign_data.dict())
        self.db.add(db_campaign)
        self.db.commit()
        self.db.refresh(db_campaign)
        return db_campaign

    async def update_campaign(self, campaign_id: int, campaign_data: CampaignUpdate) -> Optional[Campaign]:
        """Update campaign"""
        db_campaign = self.db.query(Campaign).filter(Campaign.id == campaign_id).first()
        if not db_campaign:
            return None
        
        update_data = campaign_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_campaign, field, value)
        
        self.db.commit()
        self.db.refresh(db_campaign)
        return db_campaign

    async def start_campaign(self, campaign_id: int) -> Optional[str]:
        """Start campaign execution"""
        db_campaign = self.db.query(Campaign).filter(Campaign.id == campaign_id).first()
        if not db_campaign:
            return None
        
        db_campaign.status = CampaignStatus.RUNNING
        self.db.commit()
        
        # TODO: Start Celery task for campaign execution
        return f"task_{campaign_id}"

    async def stop_campaign(self, campaign_id: int) -> bool:
        """Stop campaign execution"""
        db_campaign = self.db.query(Campaign).filter(Campaign.id == campaign_id).first()
        if not db_campaign:
            return False
        
        db_campaign.status = CampaignStatus.PAUSED
        self.db.commit()
        return True
