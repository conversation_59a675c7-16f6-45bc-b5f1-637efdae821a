version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: sms_postgres
    environment:
      POSTGRES_DB: sms_campaigns
      POSTGRES_USER: sms_user
      POSTGRES_PASSWORD: sms_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - sms_network

  # Redis for Celery
  redis:
    image: redis:7-alpine
    container_name: sms_redis
    ports:
      - "6379:6379"
    networks:
      - sms_network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: sms_backend
    environment:
      - DATABASE_URL=************************************************/sms_campaigns
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
    networks:
      - sms_network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Celery Worker
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: sms_celery_worker
    environment:
      - DATABASE_URL=************************************************/sms_campaigns
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
    networks:
      - sms_network
    command: celery -A app.core.celery worker --loglevel=info

  # Celery Beat (Scheduler)
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: sms_celery_beat
    environment:
      - DATABASE_URL=************************************************/sms_campaigns
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
    networks:
      - sms_network
    command: celery -A app.core.celery beat --loglevel=info

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: sms_frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - sms_network
    environment:
      - REACT_APP_API_URL=http://localhost:8000

volumes:
  postgres_data:

networks:
  sms_network:
    driver: bridge
