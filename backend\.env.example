# Database
DATABASE_URL=postgresql://sms_user:sms_password@localhost:5432/sms_campaigns

# Redis
REDIS_URL=redis://localhost:6379/0

# Security
SECRET_KEY=your-super-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# SMS Provider Configuration
SMS_PROVIDER=twilio  # or smsc, smsru, etc.
SMS_API_URL=https://api.twilio.com/2010-04-01/Accounts
SMS_API_KEY=your-sms-api-key
SMS_API_SECRET=your-sms-api-secret
SMS_FROM_NUMBER=+**********

# Application Settings
APP_NAME=SMS Campaign System
APP_VERSION=1.0.0
DEBUG=True
CORS_ORIGINS=["http://localhost:3000"]

# Celery Settings
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Rate Limiting
SMS_RATE_LIMIT_PER_MINUTE=100
SMS_RATE_LIMIT_PER_HOUR=1000

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
