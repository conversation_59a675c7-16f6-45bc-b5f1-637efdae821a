"""
Application configuration settings
"""

import os


class Settings:
    """Application settings"""

    def __init__(self):
        # Application
        self.APP_NAME = os.getenv("APP_NAME", "SMS Campaign System")
        self.APP_VERSION = os.getenv("APP_VERSION", "1.0.0")
        self.DEBUG = os.getenv("DEBUG", "True").lower() == "true"
        self.API_V1_STR = "/api/v1"

        # Security
        self.SECRET_KEY = os.getenv("SECRET_KEY", "your-super-secret-key-change-in-production-12345")
        self.ALGORITHM = "HS256"
        self.ACCESS_TOKEN_EXPIRE_MINUTES = 30

        # Database
        self.DATABASE_URL = os.getenv("DATABASE_URL", "************************************************/sms_campaigns")

        # Redis
        self.REDIS_URL = os.getenv("REDIS_URL", "redis://redis:6379/0")

        # CORS
        cors_origins = os.getenv("CORS_ORIGINS", "http://localhost:3000")
        self.CORS_ORIGINS = [origin.strip() for origin in cors_origins.split(",")]
        self.ALLOWED_HOSTS = ["*"]

        # SMS Provider
        self.SMS_PROVIDER = os.getenv("SMS_PROVIDER", "mock")
        self.SMS_API_URL = os.getenv("SMS_API_URL", "https://api.example.com")
        self.SMS_API_KEY = os.getenv("SMS_API_KEY", "your-sms-api-key")
        self.SMS_API_SECRET = os.getenv("SMS_API_SECRET", "your-sms-api-secret")
        self.SMS_FROM_NUMBER = os.getenv("SMS_FROM_NUMBER", "+**********")

        # Rate Limiting
        self.SMS_RATE_LIMIT_PER_MINUTE = int(os.getenv("SMS_RATE_LIMIT_PER_MINUTE", "100"))
        self.SMS_RATE_LIMIT_PER_HOUR = int(os.getenv("SMS_RATE_LIMIT_PER_HOUR", "1000"))

        # Celery
        self.CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL", "redis://redis:6379/0")
        self.CELERY_RESULT_BACKEND = os.getenv("CELERY_RESULT_BACKEND", "redis://redis:6379/0")

        # Logging
        self.LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
        self.LOG_FILE = os.getenv("LOG_FILE")


settings = Settings()
