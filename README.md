# SMS Campaign Management System

Система управления SMS рассылками с таргетингом на основе пользовательских метрик.

## Возможности

- 📱 Управление номерной базой с метриками пользователей
- 🎯 Таргетированные рассылки на основе сегментации
- 📊 Аналитика и отчеты по кампаниям
- ⚡ Асинхронная обработка массовых рассылок
- 🔌 Интеграция с SMS провайдерами
- 🖥️ Веб-интерфейс для управления

## Технический стек

- **Backend**: Python 3.11+ с FastAPI
- **База данных**: PostgreSQL 15+
- **Очереди**: Redis + Celery
- **Frontend**: React 18+
- **Контейнеризация**: Docker & Docker Compose

## Структура проекта

```
sms-campaign-system/
├── backend/                 # Python FastAPI приложение
│   ├── app/
│   │   ├── api/            # API endpoints
│   │   ├── core/           # Конфигурация и настройки
│   │   ├── models/         # SQLAlchemy модели
│   │   ├── services/       # Бизнес-логика
│   │   ├── schemas/        # Pydantic схемы
│   │   └── utils/          # Утилиты
│   ├── migrations/         # Alembic миграции
│   ├── tests/             # Тесты
│   └── requirements.txt
├── frontend/              # React приложение
├── docker/               # Docker конфигурации
├── docs/                 # Документация
└── docker-compose.yml
```

## Быстрый старт

### Вариант 1: С Docker (рекомендуется)

1. Убедитесь, что Docker Desktop запущен
2. Клонируйте репозиторий
3. Запустите `docker-compose up -d`
4. Откройте http://localhost:3000 для веб-интерфейса
5. API доступно по адресу http://localhost:8000

### Вариант 2: Локальная разработка (без Docker)

1. **Backend**:
   ```bash
   cd backend
   pip install -r requirements.txt
   python run_local.py
   ```

2. **Frontend** (в отдельном терминале):
   ```bash
   cd frontend
   npm install
   npm start
   ```

3. Откройте http://localhost:3000 для веб-интерфейса
4. API доступно по адресу http://localhost:8000

## API Документация

После запуска приложения документация доступна по адресу:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
