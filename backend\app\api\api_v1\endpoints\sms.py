"""
SMS management endpoints
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

from app.core.database import get_db
from app.schemas.sms import SMSMessage, SMSMessageCreate, SMSStatus
from app.services.sms_service import SMSService

router = APIRouter()


@router.get("/", response_model=List[SMSMessage])
async def get_sms_messages(
    skip: int = 0,
    limit: int = 100,
    campaign_id: int = None,
    status: str = None,
    db: Session = Depends(get_db)
):
    """Get list of SMS messages with filtering"""
    sms_service = SMSService(db)
    return await sms_service.get_messages(
        skip=skip, 
        limit=limit, 
        campaign_id=campaign_id, 
        status=status
    )


@router.post("/send", response_model=SMSMessage)
async def send_single_sms(
    sms_data: SMSMessageCreate,
    db: Session = Depends(get_db)
):
    """Send single SMS message"""
    sms_service = SMSService(db)
    return await sms_service.send_single_message(sms_data)


@router.get("/{message_id}", response_model=SMSMessage)
async def get_sms_message(
    message_id: int,
    db: Session = Depends(get_db)
):
    """Get SMS message by ID"""
    sms_service = SMSService(db)
    message = await sms_service.get_message(message_id)
    if not message:
        raise HTTPException(status_code=404, detail="SMS message not found")
    return message


@router.get("/{message_id}/status", response_model=SMSStatus)
async def get_sms_status(
    message_id: int,
    db: Session = Depends(get_db)
):
    """Get SMS message delivery status"""
    sms_service = SMSService(db)
    status = await sms_service.get_message_status(message_id)
    if not status:
        raise HTTPException(status_code=404, detail="SMS message not found")
    return status


@router.post("/webhook/delivery")
async def sms_delivery_webhook(
    webhook_data: dict,
    db: Session = Depends(get_db)
):
    """Webhook endpoint for SMS delivery status updates"""
    sms_service = SMSService(db)
    result = await sms_service.handle_delivery_webhook(webhook_data)
    return {"status": "processed", "result": result}
