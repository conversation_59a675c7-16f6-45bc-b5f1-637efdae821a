"""
Segment service for targeting logic
"""

from typing import List, Optional
from sqlalchemy.orm import Session
from app.models.segment import Segment
from app.models.user import User
from app.schemas.segment import SegmentCreate, SegmentUpdate


class SegmentService:
    def __init__(self, db: Session):
        self.db = db

    async def get_segments(self, skip: int = 0, limit: int = 100) -> List[Segment]:
        """Get list of segments with pagination"""
        return self.db.query(Segment).offset(skip).limit(limit).all()

    async def get_segment(self, segment_id: int) -> Optional[Segment]:
        """Get segment by ID"""
        return self.db.query(Segment).filter(Segment.id == segment_id).first()

    async def create_segment(self, segment_data: SegmentCreate) -> Segment:
        """Create new segment"""
        db_segment = Segment(**segment_data.dict())
        
        # Calculate initial user count
        user_count = await self._calculate_segment_users(db_segment.criteria)
        db_segment.user_count = user_count
        
        self.db.add(db_segment)
        self.db.commit()
        self.db.refresh(db_segment)
        return db_segment

    async def get_segment_users(self, segment_id: int) -> Optional[List[User]]:
        """Get users matching segment criteria"""
        db_segment = self.db.query(Segment).filter(Segment.id == segment_id).first()
        if not db_segment:
            return None
        
        return await self._get_users_by_criteria(db_segment.criteria)

    async def test_segment(self, segment_id: int) -> Optional[int]:
        """Test segment criteria and return matching user count"""
        db_segment = self.db.query(Segment).filter(Segment.id == segment_id).first()
        if not db_segment:
            return None
        
        return await self._calculate_segment_users(db_segment.criteria)

    async def _calculate_segment_users(self, criteria: dict) -> int:
        """Calculate number of users matching criteria"""
        query = self.db.query(User).filter(User.is_active == True)
        
        # Apply criteria filters
        # This is a simplified implementation - in production you'd want more sophisticated filtering
        for key, value in criteria.items():
            if key == "metrics":
                # Filter by metrics (simplified)
                for metric_key, metric_value in value.items():
                    query = query.filter(User.metrics[metric_key].astext == str(metric_value))
        
        return query.count()

    async def _get_users_by_criteria(self, criteria: dict) -> List[User]:
        """Get users matching criteria"""
        query = self.db.query(User).filter(User.is_active == True)
        
        # Apply criteria filters
        for key, value in criteria.items():
            if key == "metrics":
                for metric_key, metric_value in value.items():
                    query = query.filter(User.metrics[metric_key].astext == str(metric_value))
        
        return query.all()
