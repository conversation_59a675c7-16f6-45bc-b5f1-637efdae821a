"""
SMS service for message handling
"""

from typing import List, Optional
from sqlalchemy.orm import Session
from app.models.sms_message import SMSMessage, MessageStatus
from app.schemas.sms import SMSMessageCreate, SMSStatus


class SMSService:
    def __init__(self, db: Session):
        self.db = db

    async def get_messages(
        self, 
        skip: int = 0, 
        limit: int = 100, 
        campaign_id: Optional[int] = None,
        status: Optional[str] = None
    ) -> List[SMSMessage]:
        """Get list of SMS messages with filtering"""
        query = self.db.query(SMSMessage)
        
        if campaign_id:
            query = query.filter(SMSMessage.campaign_id == campaign_id)
        
        if status:
            query = query.filter(SMSMessage.status == status)
        
        return query.offset(skip).limit(limit).all()

    async def get_message(self, message_id: int) -> Optional[SMSMessage]:
        """Get SMS message by ID"""
        return self.db.query(SMSMessage).filter(SMSMessage.id == message_id).first()

    async def send_single_message(self, sms_data: SMSMessageCreate) -> SMSMessage:
        """Send single SMS message"""
        db_message = SMSMessage(**sms_data.dict())
        db_message.status = MessageStatus.PENDING
        
        self.db.add(db_message)
        self.db.commit()
        self.db.refresh(db_message)
        
        # TODO: Send to SMS provider
        # For now, just mark as sent
        db_message.status = MessageStatus.SENT
        self.db.commit()
        
        return db_message

    async def get_message_status(self, message_id: int) -> Optional[SMSStatus]:
        """Get SMS message delivery status"""
        db_message = self.db.query(SMSMessage).filter(SMSMessage.id == message_id).first()
        if not db_message:
            return None
        
        return SMSStatus(
            message_id=db_message.id,
            status=db_message.status,
            external_id=db_message.external_id,
            error_message=db_message.error_message,
            sent_at=db_message.sent_at,
            delivered_at=db_message.delivered_at
        )

    async def handle_delivery_webhook(self, webhook_data: dict) -> bool:
        """Handle SMS delivery status webhook"""
        # This would handle webhooks from SMS providers
        # Implementation depends on the specific provider
        return True
