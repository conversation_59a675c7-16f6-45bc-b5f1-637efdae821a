"""
Segment management endpoints for targeting
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

from app.core.database import get_db
from app.schemas.segment import Segment, SegmentCreate, SegmentUpdate
from app.services.segment_service import SegmentService

router = APIRouter()


@router.get("/", response_model=List[Segment])
async def get_segments(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get list of segments with pagination"""
    segment_service = SegmentService(db)
    return await segment_service.get_segments(skip=skip, limit=limit)


@router.post("/", response_model=Segment)
async def create_segment(
    segment_data: SegmentCreate,
    db: Session = Depends(get_db)
):
    """Create new segment"""
    segment_service = SegmentService(db)
    return await segment_service.create_segment(segment_data)


@router.get("/{segment_id}", response_model=Segment)
async def get_segment(
    segment_id: int,
    db: Session = Depends(get_db)
):
    """Get segment by ID"""
    segment_service = SegmentService(db)
    segment = await segment_service.get_segment(segment_id)
    if not segment:
        raise HTTPException(status_code=404, detail="Segment not found")
    return segment


@router.get("/{segment_id}/users")
async def get_segment_users(
    segment_id: int,
    db: Session = Depends(get_db)
):
    """Get users matching segment criteria"""
    segment_service = SegmentService(db)
    users = await segment_service.get_segment_users(segment_id)
    if users is None:
        raise HTTPException(status_code=404, detail="Segment not found")
    return {"segment_id": segment_id, "user_count": len(users), "users": users}


@router.post("/{segment_id}/test")
async def test_segment(
    segment_id: int,
    db: Session = Depends(get_db)
):
    """Test segment criteria and return matching user count"""
    segment_service = SegmentService(db)
    count = await segment_service.test_segment(segment_id)
    if count is None:
        raise HTTPException(status_code=404, detail="Segment not found")
    return {"segment_id": segment_id, "matching_users": count}
