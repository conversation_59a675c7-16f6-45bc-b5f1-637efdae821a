"""
Segment Pydantic schemas
"""

from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel


class SegmentBase(BaseModel):
    name: str
    description: Optional[str] = None
    criteria: Dict[str, Any]


class SegmentCreate(SegmentBase):
    pass


class SegmentUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    criteria: Optional[Dict[str, Any]] = None


class Segment(SegmentBase):
    id: int
    user_count: int = 0
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
