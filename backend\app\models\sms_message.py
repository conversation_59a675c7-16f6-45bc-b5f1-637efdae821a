"""
SMS Message model for tracking individual messages
"""

from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Text, Enum, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from app.core.database import Base


class MessageStatus(enum.Enum):
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    UNDELIVERED = "undelivered"


class SMSMessage(Base):
    __tablename__ = "sms_messages"

    id = Column(Integer, primary_key=True, index=True)
    
    # Message content
    phone_number = Column(String(20), nullable=False, index=True)
    message_text = Column(Text, nullable=False)
    
    # Status tracking
    status = Column(Enum(MessageStatus), default=MessageStatus.PENDING, index=True)
    external_id = Column(String(255))  # ID from SMS provider
    error_message = Column(Text)
    
    # Relationships
    campaign_id = Column(Integer, ForeignKey("campaigns.id"), index=True)
    user_id = Column(Inte<PERSON>, Foreign<PERSON><PERSON>("users.id"), index=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    sent_at = Column(DateTime(timezone=True))
    delivered_at = Column(DateTime(timezone=True))
    
    # Relationships
    campaign = relationship("Campaign", back_populates="messages")
    user = relationship("User")
    
    def __repr__(self):
        return f"<SMSMessage(id={self.id}, phone={self.phone_number}, status={self.status})>"
